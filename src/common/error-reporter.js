const { LoggerService } = require("./logger.service");
const { SYNC_EVENTS, SYNC_OPERATIONS } = require("../constants/syncEvents");

/**
 * Error Reporter Utility for apphero-sf-sync-service
 * Provides comprehensive error reporting and logging functionality
 */
class ErrorReporter {
  constructor(requestId, operation = SYNC_OPERATIONS.SYNC_ON_DEMAND) {
    this.requestId = requestId;
    this.operation = operation;
    this.logger = new LoggerService();
  }

  /**
   * Generate and log a comprehensive error report
   * @param {ErrorAccumulator} errorAccumulator - The error accumulator instance
   * @param {any} originalEvent - The original event that triggered the sync
   * @param {string} processName - Name of the process (e.g., "On-Demand Sync", "CMS Sync")
   * @returns {Promise<Object>} The comprehensive report
   */
  async generateComprehensiveReport(
    errorAccumulator,
    originalEvent,
    processName
  ) {
    const stats = errorAccumulator.getStats();
    const hasErrors = errorAccumulator.hasErrors();
    const hasWarnings = errorAccumulator.hasWarnings();

    const report = {
      requestId: this.requestId,
      processName,
      timestamp: new Date().toISOString(),
      summary: stats,
      hasErrors,
      hasWarnings,
      details: {
        errors: errorAccumulator.getErrors(),
        warnings: errorAccumulator.getWarnings(),
        errorSummary: errorAccumulator.getErrorSummary(),
      },
      recommendations: this.generateRecommendations(errorAccumulator),
    };

    // Log the comprehensive report
    if (hasErrors) {
      await this.logger.errorSync(
        SYNC_EVENTS.COMPREHENSIVE_ERROR_REPORT,
        originalEvent,
        report,
        `${processName} completed with ${stats.errorCount} errors and ${stats.warningCount} warnings. Success rate: ${stats.successRate}%`,
        this.requestId,
        "error_report",
        "requestId",
        report,
        this.operation
      );
    } else if (hasWarnings) {
      await this.logger.logSync(
        SYNC_EVENTS.SYNC_PARTIAL_SUCCESS,
        originalEvent,
        report,
        `${processName} completed with ${stats.warningCount} warnings. Success rate: ${stats.successRate}%`,
        this.requestId,
        "warning_report",
        "requestId",
        report,
        this.operation
      );
    } else {
      await this.logger.logSync(
        SYNC_EVENTS.SYNC_COMPLETED,
        originalEvent,
        report,
        `${processName} completed successfully. Processed: ${stats.processedCount}, Success rate: ${stats.successRate}%`,
        this.requestId,
        "success_report",
        "requestId",
        report,
        this.operation
      );
    }

    return report;
  }

  /**
   * Generate actionable recommendations based on error patterns
   * @param {ErrorAccumulator} errorAccumulator - The error accumulator instance
   * @returns {Array<string>} Array of recommendations
   */
  generateRecommendations(errorAccumulator) {
    const recommendations = [];
    const errorSummary = errorAccumulator.getErrorSummary();
    const errors = errorAccumulator.getErrors();

    // Analyze error patterns and generate recommendations
    Object.values(errorSummary).forEach((summary) => {
      const { operation, entityType, count, messages } = summary;

      // Network/timeout related errors
      if (
        messages.some(
          (msg) =>
            msg.toLowerCase().includes("timeout") ||
            msg.toLowerCase().includes("network")
        )
      ) {
        recommendations.push(
          `Consider increasing timeout values for ${operation} operations on ${entityType}`
        );
        recommendations.push(
          `Check network connectivity and Salesforce API limits`
        );
      }

      // Authentication/permission errors
      if (
        messages.some(
          (msg) =>
            msg.toLowerCase().includes("auth") ||
            msg.toLowerCase().includes("permission")
        )
      ) {
        recommendations.push(
          `Verify authentication credentials and permissions for ${entityType} operations`
        );
      }

      // Rate limiting errors
      if (
        messages.some(
          (msg) =>
            msg.toLowerCase().includes("rate limit") ||
            msg.toLowerCase().includes("too many requests")
        )
      ) {
        recommendations.push(
          `Implement exponential backoff for ${operation} operations`
        );
        recommendations.push(
          `Consider reducing batch sizes or adding delays between requests`
        );
      }

      // Data validation errors
      if (
        messages.some(
          (msg) =>
            msg.toLowerCase().includes("validation") ||
            msg.toLowerCase().includes("required field")
        )
      ) {
        recommendations.push(`Review data validation rules for ${entityType}`);
        recommendations.push(
          `Ensure all required fields are populated before sync`
        );
      }

      // High error count recommendations
      if (count > 10) {
        recommendations.push(
          `High error count (${count}) for ${operation} on ${entityType} - consider investigating data quality`
        );
      }
    });

    // General recommendations based on error count
    const totalErrors = errors.length;
    if (totalErrors > 50) {
      recommendations.push(
        `High total error count (${totalErrors}) - consider running sync in smaller batches`
      );
      recommendations.push(`Review system health and resource availability`);
    }

    // Remove duplicates
    return [...new Set(recommendations)];
  }

  /**
   * Generate a summary email/notification content
   * @param {ErrorAccumulator} errorAccumulator - The error accumulator instance
   * @param {string} processName - Name of the process
   * @returns {Object} Email content object
   */
  generateNotificationContent(errorAccumulator, processName) {
    const stats = errorAccumulator.getStats();
    const hasErrors = errorAccumulator.hasErrors();
    const errorSummary = errorAccumulator.getErrorSummary();

    const subject = hasErrors
      ? `🚨 ${processName} Failed - ${stats.errorCount} Errors`
      : `✅ ${processName} Completed Successfully`;

    const body = `
${processName} Report
Request ID: ${this.requestId}
Timestamp: ${new Date().toISOString()}

📊 Summary:
- Total Processed: ${stats.processedCount}
- Successful: ${stats.successCount}
- Failed: ${stats.failureCount}
- Warnings: ${stats.warningCount}
- Success Rate: ${stats.successRate}%

${
  hasErrors
    ? `
🚨 Error Details:
${Object.values(errorSummary)
  .map(
    (summary) =>
      `- ${summary.operation} (${summary.entityType}): ${summary.count} errors
    Messages: ${summary.messages.join(", ")}`
  )
  .join("\n")}

🔧 Recommendations:
${this.generateRecommendations(errorAccumulator)
  .map((rec) => `- ${rec}`)
  .join("\n")}
`
    : ""
}

${
  stats.warningCount > 0
    ? `
⚠️ Warnings: ${stats.warningCount} warnings were recorded during processing.
`
    : ""
}

For detailed logs, check CloudWatch logs with Request ID: ${this.requestId}
    `.trim();

    return {
      subject,
      body,
      priority: hasErrors ? "high" : "normal",
      requestId: this.requestId,
      stats,
    };
  }

  /**
   * Log error summary by entity type
   * @param {ErrorAccumulator} errorAccumulator - The error accumulator instance
   * @param {any} originalEvent - The original event
   */
  async logErrorSummaryByEntity(errorAccumulator, originalEvent) {
    const errorSummary = errorAccumulator.getErrorSummary();

    for (const [key, summary] of Object.entries(errorSummary)) {
      await this.logger.errorSync(
        SYNC_EVENTS.ERROR_SUMMARY_GENERATED,
        originalEvent,
        summary,
        `${summary.operation} failed for ${summary.count} ${
          summary.entityType
        } records: ${summary.messages.join(", ")}`,
        this.requestId,
        summary.entityType.toLowerCase(),
        "entityType",
        summary,
        this.operation
      );
    }
  }

  /**
   * Create a structured error report for external systems
   * @param {ErrorAccumulator} errorAccumulator - The error accumulator instance
   * @param {string} processName - Name of the process
   * @returns {Object} Structured error report
   */
  createStructuredReport(errorAccumulator, processName) {
    const stats = errorAccumulator.getStats();
    const errors = errorAccumulator.getErrors();
    const warnings = errorAccumulator.getWarnings();

    return {
      metadata: {
        requestId: this.requestId,
        processName,
        timestamp: new Date().toISOString(),
        operation: this.operation,
      },
      metrics: {
        totalProcessed: stats.processedCount,
        successful: stats.successCount,
        failed: stats.failureCount,
        warnings: stats.warningCount,
        successRate: parseFloat(stats.successRate),
      },
      errors: errors.map((error) => ({
        timestamp: error.timestamp,
        operation: error.operation,
        entityType: error.entityType,
        entityId: error.entityId,
        message: error.message,
        context: error.context,
      })),
      warnings: warnings.map((warning) => ({
        timestamp: warning.timestamp,
        operation: warning.operation,
        entityType: warning.entityType,
        entityId: warning.entityId,
        message: warning.message,
        context: warning.context,
      })),
      recommendations: this.generateRecommendations(errorAccumulator),
    };
  }
}

module.exports = { ErrorReporter };
